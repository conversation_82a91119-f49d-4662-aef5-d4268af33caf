import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middlewares/trycatch";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";
import { createPlanSchema, updatePlanSchema } from "../../../validations/plan.validation";
import db from "../../../config/db";
import { TABLE } from "../../../utils/Database/table";
import { addYears } from "date-fns";
import { getAllPlansData, getPlanByIdData } from "../../../services/plan.service";

const MAX_PER_TYPE = 5;

// Create Plan
export const createPlan = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const parsed = createPlanSchema.safeParse(req.body);
    if (!parsed.success) {
      sendResponse(res, 400, "Validation error", false, { errors: parsed.error.format() });
      return;
    }

    const { name, type, duration_years } = parsed.data;

    // Enforce max per type
    const countResult = (await db(TABLE.PLANS)
      .where({ type })
      .count("id as count")) as Array<{ count: string }>;
    const currentCount = Number(countResult[0].count);
    if (currentCount >= MAX_PER_TYPE) {
      sendResponse(res, 400, `Cannot create more than ${MAX_PER_TYPE} ${type} plans`, false);
      return;
    }

    // Compute expiration_date for aligners
    const expiration_date =
      type === "aligner" && typeof duration_years === "number"
        ? addYears(new Date(), duration_years)
        : null;

    try {
      const [newPlan] = await db(TABLE.PLANS)
        .insert({
          name,
          type,
          duration_years: duration_years || null,
          expiration_date,
        })
        .returning([
          "id",
          "name",
          "type",
          "duration_years",
          "expiration_date",
          "created_at",
          "updated_at",
        ]);

      sendResponse(res, 201, "Plan created successfully", true, newPlan);
    } catch (error: any) {
      // Check for duplicate key error
      if (error.code === "23505") {
        sendResponse(res, 400, "A plan with this name already exists.", false);
        return;
      }
      throw error; // Let global handler catch unexpected errors
    }
  }
);
// Get All Plans
export const getAllPlans = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const page = parseInt((req.query.page as string) || "1", 10);
    const limit = parseInt((req.query.limit as string) || "10", 10);
    const data = await getAllPlansData(page, limit);
    sendResponse(res, 200, "Plans fetched successfully", true, data);
  }
);

// Get Plan by ID
export const getPlanById = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const plan = await getPlanByIdData(id);

    if (!plan) {
      sendResponse(res, 404, "Plan not found", false);
      return;
    }
    sendResponse(res, 200, "Plan fetched successfully", true, plan);
  }
);

// Update Plan
export const updatePlan = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const parsed = updatePlanSchema.safeParse(req.body);
    if (!parsed.success) {
      sendResponse(res, 400, "Validation error", false, { errors: parsed.error.format() });
      return;
    }
    const updates: any = parsed.data;

    const existing = (await db(TABLE.PLANS).where({ id }).first()) as any;
    if (!existing) {
      sendResponse(res, 404, "Plan not found", false);
      return;
    }

    // If changing type, enforce max
    if (updates.type && updates.type !== existing.type) {
      const countResult = (await db(TABLE.PLANS)
        .where({ type: updates.type })
        .count("id as count")) as Array<{ count: string }>;
      const typeCount = Number(countResult[0].count);
      if (typeCount >= MAX_PER_TYPE) {
        sendResponse(res, 400, `Cannot have more than ${MAX_PER_TYPE} ${updates.type} plans`, false);
        return;
      }
    }

    // Recompute expiration_date if aligner involved
    if (updates.type === "aligner" || existing.type === "aligner") {
      const years = updates.duration_years ?? existing.duration_years ?? 0;
      const createdAt = typeof existing.created_at === 'string'
        ? new Date(existing.created_at)
        : existing.created_at;
      updates.expiration_date = addYears(createdAt, years);
    }

    try {
      const [updated] = await db(TABLE.PLANS)
        .where({ id })
        .update(updates)
        .returning([
          "id",
          "name",
          "type",
          "duration_years",
          "expiration_date",
          "updated_at",
        ]);

      sendResponse(res, 200, "Plan updated successfully", true, updated);
    } catch (error: any) {
      if (error.code === "23505") {
        sendResponse(res, 400, "A plan with this name already exists.", false);
        return;
      }
      throw error;
    }
  }
);
// Delete Plan
export const destroyPlanById = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const deleted = await db(TABLE.PLANS).where({ id }).del();
    if (!deleted) {
      sendResponse(res, 404, "Plan not found", false);
      return;
    }
    sendResponse(res, 200, "Plan deleted successfully", true);
  }
);
