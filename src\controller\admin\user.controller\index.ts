import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middlewares/trycatch";
import bcrypt from "bcryptjs";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";
import { UserRole } from "../../../utils/enums/users.enum";
import validate from "../../../validations";
import {
  updateUserSchema,
} from "../../../validations/user.validation";
import { TABLE } from "../../../utils/Database/table";
import db from "../../../config/db";

const BASE_URL = process.env.BASE_URL || "http://localhost:3000/uploads";

const fileFields = [
  "stlFile1",
  "stlFile2",
  "cbctFile",
  "profileRepose",
  "buccalRight",
  "buccalLeft",
  "frontalRepose",
  "frontalSmiling",
  "labialAnterior",
  "occlussalLower",
  "occlussalUpper",
  "radioGraph1",
  "radioGraph2"
];

function addBaseUrlToFiles(patient: any) {
  if (!patient) return patient;
  fileFields.forEach((field) => {
    if (patient[field]) {
      patient[field] = `${BASE_URL}/patients/${patient[field]}`;
    }
  });
  return patient;
}


// Login User
export const createUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { first_name, last_name, email, password, role_id, specialist_id } = req.body;

      // Email check
      if (await db(TABLE.USERS).where({ email }).first()) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // Generate 7‑digit unique ID
      let user_uuid: string;
      do {
        user_uuid = Math.floor(1000000 + Math.random() * 9000000).toString();
      } while (await db(TABLE.USERS).where({ user_uuid }).first());

      console.log("⏺️ About to insert user_uuid:", user_uuid);

      // Hash pwd and insert
      const hashedPassword = await bcrypt.hash(password, 10);
      const [newUser] = await db(TABLE.USERS)
        .insert({
          user_uuid,
          first_name,
          last_name,
          email,
          password: hashedPassword,
          role: role_id,
          specialist_id: role_id === UserRole.DOCTOR ? specialist_id : null,
        })
        .returning([
          "id", "user_uuid", "first_name", "last_name", "email", "role", "specialist_id"
        ]);

      // Step 6: Extra DB fetch to confirm saved correctly
      const dbCheck = await db(TABLE.USERS).where({ id: newUser.id }).first();
      console.log("🎯 Row from DB:", dbCheck);

      sendResponse(res, 201, "User created successfully", true, newUser);
    } catch (error: any) {
      console.error("❌ Error creating user:", error);
      sendResponse(res, 500, error.message, false);
    }
  }
);



export const updateUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const validationResult = validate(updateUserSchema, req.body, res);

      if (!validationResult.success) {
        sendResponse(res, 400, "Validation error", false);
        return;
      }

      const { first_name, last_name, email, username } = validationResult.data;

      // Check if the email is unique (exclude the current user by their ID)
      if (email) {
        const existingUser = await db(TABLE.USERS)
          .where({ email: email })
          .whereNot("id", id)
          .first();

        if (existingUser) {
          sendResponse(res, 400, "Email is already exists", false);
          return;
        }
      }

      const updatedUser = await db(TABLE.USERS)
        .where({ id }) // Find user by ID
        .update({
          first_name,
          last_name,
          email,
          username
        })
        .returning([
          "id",
          "first_name",
          "last_name",
          "email",
          "is_active",
          "username",
          "is_verified",
        ]);

      if (!updatedUser) {
        sendResponse(res, 400, "User not found", false);
        return;
      }

      sendResponse(res, 200, "User updated successfully", true, updatedUser);
      return;
    } catch (error: any) {
      console.log(error);
      sendResponse(res, 500, error.message, false);
      return;
    }
  }
);

export const getAllUsers = asyncHandler(async (req: Request, res: Response) => {
  try {
    console.log("Update User Request Body:", req.body);

    const { page, limit } = req.query;

    const pageSize = parseInt(page as string) || 1;
    const pageLimit = parseInt(limit as string) || 10;
    const skip = (pageSize - 1) * pageLimit;

    // Fetch paginated users based on admin's user ID
    const users = await db(TABLE.USERS)
      .select(
        "id",
        "first_name",
        "last_name",
        "email",
        "role_id",
        "is_active",
        "is_verified"
      )
      .offset(skip)
      .limit(pageLimit)
      .orderBy("created_at", "desc");

    // Fetch the total count for pagination
    const totalCountResult = await db(TABLE.USERS).count({ count: "*" });

    const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

    const data = {
      users,
      page: pageSize,
      limit: pageLimit,
      totalCount,
      totalPages: Math.ceil(totalCount / pageLimit),
    };

    sendResponse(res, 200, "User fetched successfully", true, data);
    return;
  } catch (error: any) {
    console.error(error);
    sendResponse(res, 500, error.message, false);
    return;
  }
});

export const getUsersById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Fetch user by ID
      const user = await db(TABLE.USERS)
        .select(
          "id",
          "user_uuid",
          "first_name",
          "last_name",
          "email",
          "role_id",
          "username",
          "is_active",
          "is_verified"
        )
        .where("id", id)
        .first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      sendResponse(res, 200, "User fetched successfully", true, user);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const destroyUserById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Find the user to delete
      const user = await db(TABLE.USERS).where("id", id).first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      // Delete user from the database
      await db(TABLE.USERS).where("id", id).del();

      sendResponse(res, 200, "User deleted successfully", user);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllDoctors = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Step 1: Get the 'doctor' role ID
      const doctorRole = await db(TABLE.ROLES)
        // .select("id")
        .where("role_name", "doctor")
        .first();

      if (!doctorRole) {
        sendResponse(res, 500, "Role 'doctor' not found in roles table", false);
        return;
      }

      // Step 2: Fetch users with role_id = doctorRole.id
      const doctors = await db(TABLE.USERS)
        .leftJoin(TABLE.ROLES, "users.role_id", "roles.id") // join to fetch role name
        .select(
          "users.id",
          "users.user_uuid",
          "users.first_name",
          "users.last_name",
          "users.email",
          "users.specialist_id",
          "users.is_active",
          "users.username",
          "users.is_verified",
          "users.created_at",
          "roles.role_name as role"
        )
        .where("users.role_id", doctorRole.id)
        .orderBy("users.created_at", "desc");

      // Step 3: Return result
      sendResponse(res, 200, "Doctors fetched successfully", true, doctors);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllSpecialists = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Step 1: Get the 'doctor' role ID
      const doctorRole = await db(TABLE.ROLES)
        // .select("id")
        .where("role_name", "specialist")
        .first();

      if (!doctorRole) {
        sendResponse(res, 500, "Role 'doctor' not found in roles table", false);
        return;
      }

      // Step 2: Fetch users with role_id = doctorRole.id
      const doctors = await db(TABLE.USERS)
        .leftJoin(TABLE.ROLES, "users.role_id", "roles.id") // join to fetch role name
        .select(
          "users.id",
          "users.user_uuid",
          "users.first_name",
          "users.last_name",
          "users.email",
          "users.specialist_id",
          "users.is_active",
          "users.username",
          "users.is_verified",
          "users.created_at",
          "roles.role_name as role"
        )
        .where("users.role_id", doctorRole.id)
        .orderBy("users.created_at", "desc");

      // Step 3: Return result
      sendResponse(res, 200, "Specialists fetched successfully", true, doctors);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getPatientById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      let patient = await db(TABLE.PATIENTS)
        .where({ id })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // Add file URLs
      patient = addBaseUrlToFiles(patient);

      // Fetch plan info
      let plan = null;
      if (patient.plan_id) {
        plan = await db(TABLE.PLANS).where({ id: patient.plan_id }).first();
      }

      // Fetch ship_to_office and bill_to_office addresses
      let shipToOffice = null;
      if (patient.ship_to_office) {
        shipToOffice = await db("doctor_addresses")
          .where({ id: patient.ship_to_office })
          .first();
      }
      let billToOffice = null;
      if (patient.bill_to_office) {
        billToOffice = await db("doctor_addresses")
          .where({ id: patient.bill_to_office })
          .first();
      }

      sendResponse(res, 200, "Patient fetched successfully", true, {
        ...patient,
        plan,
        ship_to_office: shipToOffice,
        bill_to_office: billToOffice,
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// export const getAllPatientsWithDoctors = asyncHandler(
//   async (req: Request, res: Response) => {
//     try {
//       // Fetch all patients with their doctors
//       let patients = await db(TABLE.PATIENTS)
//         .join(TABLE.USERS, `${TABLE.PATIENTS}.doctor_id`, '=', `${TABLE.USERS}.id`)
//         .select(
//           `${TABLE.PATIENTS}.*`,
//           `${TABLE.USERS}.first_name as doctor_first_name`,
//           `${TABLE.USERS}.last_name as doctor_last_name`
//         )
//         .orderBy(`${TABLE.PATIENTS}.created_at`, 'desc');

//       // Add file URLs and fetch all versions for each patient
//       patients = await Promise.all(
//         patients.map(async (patient: any) => {
//           // Add file URLs
//           const patientWithFiles = addBaseUrlToFiles(patient);

//           // Fetch all versions for this patient
//           const versions = await db(TABLE.PATIENTS_VERSIONS)
//             .where({ patient_id: patient.id })
//             .orderBy('version_number', 'asc');

//           return {
//             ...patientWithFiles,
//             versions,
//           };
//         })
//       );

//       sendResponse(res, 200, "Patients with doctors fetched successfully", true, patients);
//     } catch (error: any) {
//       console.error(error);
//       sendResponse(res, 500, error.message, false);
//     }
//   }
// );
export const getAllPatientsWithDoctors = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Fetch only patients who have at least one version
      let patients = await db(TABLE.PATIENTS)
        .join(TABLE.USERS, `${TABLE.PATIENTS}.doctor_id`, '=', `${TABLE.USERS}.id`)
        .join(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, '=', `${TABLE.PATIENTS_VERSIONS}.patient_id`)
        .select(
          `${TABLE.PATIENTS}.*`,
          `${TABLE.USERS}.first_name as doctor_first_name`,
          `${TABLE.USERS}.last_name as doctor_last_name`
        )
        .groupBy(
          `${TABLE.PATIENTS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`
        )
        .orderBy(`${TABLE.PATIENTS}.created_at`, 'desc');

      // Add file URLs and fetch all versions for each patient
      patients = await Promise.all(
        patients.map(async (patient: any) => {
          // Add file URLs
          const patientWithFiles = addBaseUrlToFiles(patient);

          // Fetch all versions for this patient
          const versions = await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patient.id })
            .orderBy('version_number', 'asc');

          return {
            ...patientWithFiles,
            versions,
          };
        })
      );

      sendResponse(res, 200, "Patients with doctors fetched successfully", true, patients);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllRoles = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const roles = await db(TABLE.ROLES).select('*').orderBy('id', 'asc');
      sendResponse(res, 200, 'Roles fetched successfully', true, roles);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const cancelPatientVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params; // patient ID

      // Check if patient exists
      const patient = await db(TABLE.PATIENTS)
        .where({ id })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // Get the latest patient version for this patient
      // ...existing code...
      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: id })
        .orderBy('version_number', 'desc') // or 'created_at', 'desc'
        .first();

      if (!latestVersion) {
        return sendResponse(res, 404, "No patient version found", false);
      }

      // Check if the current status is 'sent_by_doctor'
      if (latestVersion.status !== "sent_by_doctor") {
        return sendResponse(
          res,
          400,
          `Cannot cancel version with status '${latestVersion.status}'. Only versions with status 'sent_by_doctor' can be cancelled.`,
          false
        );
      }

      // Update the status to 'cancelled_by_admin'
      const [updatedVersion] = await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: latestVersion.id })
        .update({
          status: "cancelled_by_admin"
        })
        .returning("*");

      sendResponse(
        res,
        200,
        "Patient version cancelled successfully",
        true,
        {
          patient_id: id,
          version_id: updatedVersion.id,
          version_number: updatedVersion.version_number,
          old_status: "sent_by_doctor",
          new_status: "cancelled_by_admin"
        }
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

