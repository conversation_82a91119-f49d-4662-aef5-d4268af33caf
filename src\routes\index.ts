import { Router } from "express";
import AUTHROUTES from "./auth.routes";
import DOCTORROUTES from "./doctor.routes";
import PERMISSIONROUTES from "./permission.routes";
import SPECIA<PERSON>ISTROUTES from "./specialist.routes";
import ADMINPLANROUTES from "./admin/plan.routes";
import USERROUTES from "./admin/user.routes";
import { authAdminMiddleware } from "../middlewares/authAdminMiddleware";
import ADMINDASHBOARDROUTES from "./admin/dashboard.routes";
import OSSRoutes from "./oss.routes";

const router = Router();

router.use("/auth", AUTHROUTES);
router.use("/admin", ADMINPLANROUTES);
router.use("/doctor", DOCTORROUTES);
router.use("/permissions", PERMISSIONROUTES);
router.use("/specialist", SPECIALISTROUTES);
router.use("/user", authAdminMiddleware, USERROUTES);
router.use("/admin", ADMINDASHBOARDROUTES);
router.use("/file", OSSRoutes);

export default router;
