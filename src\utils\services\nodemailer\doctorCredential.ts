import dotenv from "dotenv";
import { sendEmail } from "./index";

dotenv.config();

const {
  SMTP_HOST = "smtp-relay.brevo.com",
  SMTP_PORT = "587",
  SMTP_SECURE = "false",
  SMTP_USER = "<EMAIL>",
  SMTP_PASS = "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",
  EMAIL_FROM = "<EMAIL>"
} = process.env;

// Removed Nodemailer and transporter setup

interface DoctorCredentials {
  email: string;
  password: string;
  name: string;
  role?: string; // Add optional role parameter
}

interface PasswordResetData {
  email: string;
  name: string;
  password: string;
}

export const sendDoctorCredentialEmail = async (doctorData: DoctorCredentials) => {
  const { email, password, name, role } = doctorData;

  // Determine the greeting and account type based on role
  const greeting = role === "specialist" ? "Hello Specialist" : "Hello Doctor";
  const accountType = role === "specialist" ? "specialist" : "doctor";
  
  const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Welcome to Orthodontic Platform</h2>
        <p>${greeting} ${name},</p>
        <p>Your ${accountType} account has been created. Below are your login credentials:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please log in and change your password immediately for security purposes.</p>
        <p>If you didn't expect this email, please contact the admin.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `;

  await sendEmail({
    to: email,
    subject: `Your ${role === "specialist" ? "Specialist" : "Doctor"} Account Credentials`,
    html: htmlContent,
    senderName: "Orthodontic Team",
    senderEmail: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
  });
};

export const sendPasswordResetEmail = async (resetData: PasswordResetData) => {
  const { email, name, password } = resetData;

  const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Password Reset Successful</h2>
        <p>Hello ${name},</p>
        <p>Your password has been reset. Here are your new login details:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please change your password after logging in for security reasons.</p>
        <p>If you didn't request this reset, please contact support immediately.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `;

  await sendEmail({
    to: email,
    subject: "Your New Password",
    html: htmlContent,
    senderName: "Orthodontic Team",
    senderEmail: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
  });
};

